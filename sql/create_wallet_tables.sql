-- 创建钱包表以存储用户钱包信息
CREATE TABLE IF NOT EXISTS wallets (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL UNIQUE,  -- 对应于Supabase中的auth.users.id
  balance INTEGER NOT NULL DEFAULT 0,  -- 当前钱包积分余额（整数）
  created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),  -- 创建时间戳
  updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))  -- 最后更新时间戳
);

-- 在user_id上创建索引以加快查询速度
CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON wallets(user_id);

-- 创建交易表以存储所有钱包交易
CREATE TABLE IF NOT EXISTS transactions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,  -- 对应于Supabase中的auth.users.id
  type TEXT NOT NULL,  -- "deposit"用于增加积分，"consumption"用于使用积分
  amount INTEGER NOT NULL,  -- 交易积分数量
  balance_after INTEGER NOT NULL,  -- 本次交易后的积分余额
  description TEXT,  -- 交易的可选描述
  metadata TEXT,  -- 带有附加交易元数据的JSON字符串
  created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))  -- 交易时间戳
);

-- 创建索引以加速查询
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
