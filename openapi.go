package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"

	"github.com/sashabaranov/go-openai"
	"github.com/supabase-community/auth-go/types"
)

// 字节的模型列表
var BYTEDANCE_MODELS_MAP = map[string]int{
	"deepseek-v3-250324":               1,
	"deepseek-r1-250120":               2,
	"doubao-1-5-vision-pro-32k-250115": 3,
	"doubao-1.5-vision-pro-250328":     4,
	"doubao-seed-1-6-250615":           5,
	"doubao-seed-1-6-flash-250615":     6,
}

// 阿里的模型列表
var ALIBABA_MODELS_MAP = map[string]int{
	"qwen-vl-plus":            11,
	"qwen-vl-plus-2025-01-25": 12,
	"qwen-vl-max":             13,
	"qwen-vl-max-2025-04-02":  14,
	"qvq-max":                 15,
	"qvq-max-2025-03-25":      16,
}

// 默认成本倍率
const DEFAULT_COST_MULTIPLIER = 2

// 全局模型价格映射
var MODEL_PRICING_MAP = map[string]ModelPricing{
	// 字节模型价格
	"doubao-seed-1-6-250615":           {PromptPrice: 0.0008 / 1000, CompletionPrice: 0.0008 / 1000},
	"doubao-seed-1-6-flash-250615":     {PromptPrice: 0.00015 / 1000, CompletionPrice: 0.00015 / 1000},
	"doubao-1.5-vision-pro-250328":     {PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000},
	"doubao-1-5-vision-pro-32k-250115": {PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000},
	"deepseek-v3-250324":               {PromptPrice: 0.002 / 1000, CompletionPrice: 0.008 / 1000},
	"deepseek-r1-250120":               {PromptPrice: 0.004 / 1000, CompletionPrice: 0.016 / 1000},
	// 阿里模型价格
	"qwen-vl-plus":            {PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000},
	"qwen-vl-plus-2025-01-25": {PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000},
	"qwen-vl-max":             {PromptPrice: 0.006 / 1000, CompletionPrice: 0.018 / 1000},
	"qwen-vl-max-2025-04-02":  {PromptPrice: 0.006 / 1000, CompletionPrice: 0.018 / 1000},
	"qvq-max":                 {PromptPrice: 0.006 / 1000, CompletionPrice: 0.018 / 1000},
	"qvq-max-2025-03-25":      {PromptPrice: 0.006 / 1000, CompletionPrice: 0.018 / 1000},
}

// 默认价格
var DEFAULT_PRICING = ModelPricing{PromptPrice: 0.003 / 1000, CompletionPrice: 0.009 / 1000}

// ChatRequest 聊天请求结构
type ChatRequest struct {
	Model       string  `json:"model"`
	Text        string  `json:"text"`
	PromptKey   string  `json:"prompt_key"`
	Content     string  `json:"content"`
	ContentType string  `json:"content_type"`
	Temperature float32 `json:"temperature"`
}

// OpenAIImageContent 表示OpenAI API图像内容
type OpenAIImageContent struct {
	URL string `json:"url"`
}

// OpenAIChoice 表示OpenAI API响应选择
type OpenAIChoice struct {
	Message struct {
		Content string `json:"content"`
	} `json:"message"`
}

// SimplifiedResponse 表示简化的响应
type SimplifiedResponse struct {
	ID        string `json:"id"`
	OcrResult string `json:"ocr_result"`
	Content   string `json:"content"`
	Balance   int    `json:"balance"`
}

// ModelPricing 表示模型价格
type ModelPricing struct {
	PromptPrice     float64
	CompletionPrice float64
}

// GetModelPricing 获取特定模型的价格
func GetModelPricing(model string) ModelPricing {
	// 根据模型名称选择价格
	pricing, exists := MODEL_PRICING_MAP[model]
	if !exists {
		return DEFAULT_PRICING
	}
	return pricing
}

func ChatHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "", http.StatusUnauthorized)
		return
	}
	// 检查用户钱包余额
	firstBalance, err := pg.GetUserBalance(user.ID.String())
	if err != nil {
		log.Printf("获取余额失败: %v\n", err)
		http.Error(w, "Failed to get balance", http.StatusInternalServerError)
		return
	}
	// 检查余额是否足够
	if firstBalance <= 100 { // 0.1元 = 100积分
		http.Error(w, "Insufficient balance", http.StatusPaymentRequired)
		return
	}
	// 解析请求体
	var requestBody ChatRequest
	if err = json.NewDecoder(r.Body).Decode(&requestBody); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}
	// 验证请求
	if requestBody.Text == "" || requestBody.PromptKey == "" || requestBody.ContentType == "" {
		http.Error(w, "Invalid request: missing required fields", http.StatusBadRequest)
		return
	}
	// 使用默认模型如果未指定
	model := requestBody.Model
	if model == "" {
		model = "deepseek-v3-250324"
	}
	// 使用默认温度如果未指定
	if requestBody.Temperature == 0 {
		requestBody.Temperature = 0.3
	}
	// 根据PromptKey参数从数据库选择不同的systemPrompt
	requestBody.PromptKey, _ = pg.GetSystemPromptForSubject(requestBody.PromptKey)
	// 创建上下文
	ctx := r.Context()
	// 发送聊天完成请求
	openaiResponse, err := CreateChatCompletion(ctx, requestBody)
	if err != nil {
		http.Error(w, fmt.Sprintf("API error: %v", err), http.StatusInternalServerError)
		return
	}
	// 获取模型价格
	pricing := GetModelPricing(model)
	// 计算API调用成本（积分制）
	cost := calculateAPICostInPoints(openaiResponse.Usage, pricing)
	// 创建消费元数据
	metadata := map[string]any{
		"model":             model,
		"prompt_tokens":     openaiResponse.Usage.PromptTokens,
		"completion_tokens": openaiResponse.Usage.CompletionTokens,
		"total_tokens":      openaiResponse.Usage.TotalTokens,
		"prompt_key":        requestBody.PromptKey,
		"cost_multiplier":   DEFAULT_COST_MULTIPLIER,
	}
	// 记录消费
	consumeResult, err := pg.CheckAndConsume(
		user.ID.String(),
		cost,
		"使用"+model+"模型",
		metadata,
	)
	if err != nil {
		// 如果记录消费失败，记录错误但仍然返回响应
		log.Printf("记录消费失败: %v\n", err)
	}
	// 获取更新后的余额
	var balance int
	if consumeResult != nil && consumeResult["success"].(bool) {
		balance = consumeResult["balance"].(int)
	} else {
		// 如果消费失败或未记录消费，尝试获取当前余额
		balance, err = pg.GetUserBalance(user.ID.String())
	}
	// 提取需要的字段
	simplifiedResponse := SimplifiedResponse{
		ID:      openaiResponse.ID,
		Content: openaiResponse.Choices[0].Message.Content,
		Balance: balance,
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	// 检查是否有刷新令牌
	refreshToken := r.Header.Get("X-Refresh-Token")
	if refreshToken != "" {
		// 调用Supabase Auth API进行令牌刷新
		resp, err := authClient.client.RefreshToken(refreshToken)
		if err == nil && resp != nil {
			// 将新的访问令牌放入响应头
			w.Header().Set("X-New-Access-Token", resp.AccessToken)
		}
	}
	json.NewEncoder(w).Encode(simplifiedResponse)
}

// 计算API调用成本（积分制）
func calculateAPICostInPoints(usage openai.Usage, pricing ModelPricing) int {
	// 计算提示和完成的成本（元）
	promptCost := float64(usage.PromptTokens) * pricing.PromptPrice
	completionCost := float64(usage.CompletionTokens) * pricing.CompletionPrice
	// 应用成本倍率
	totalCostYuan := (promptCost + completionCost) * DEFAULT_COST_MULTIPLIER
	// 转换为积分（1元=1000积分）并向上取整
	totalCostPoints := max(int(math.Ceil(totalCostYuan*1000)), 1)
	return totalCostPoints
}

// containsKey 检查字符串是否在map中
func containsKey(models map[string]int, items string) bool {
	if _, ok := models[items]; ok {
		return true
	}
	return false
}

func modelId(key string) int {
	if v, ok := BYTEDANCE_MODELS_MAP[key]; ok {
		return v
	}
	if v, ok := ALIBABA_MODELS_MAP[key]; ok {
		return v
	}
	return 0
}

// AnalysisRequest 表示分析请求的内部结构
type AnalysisRequest struct {
	User        *types.UserResponse
	RequestBody ChatRequest
	Context     context.Context
}

// AnalysisResult 表示分析结果的内部结构
type AnalysisResult struct {
	OcrResult        string
	AnalysisResponse *openai.ChatCompletionResponse
	OcrResponse      *openai.ChatCompletionResponse
	OcrModel         string
	AnalysisModel    string
	TotalCost        int
}

// validateAnalysisRequest 验证分析请求
func validateAnalysisRequest(w http.ResponseWriter, r *http.Request) (*AnalysisRequest, bool) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
		return nil, false
	}
	// 从上下文中获取用户信息
	user, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "", http.StatusUnauthorized)
		return nil, false
	}
	// 检查用户钱包余额
	firstBalance, err := pg.GetUserBalance(user.ID.String())
	if err != nil {
		log.Printf("获取余额失败: %v\n", err)
		http.Error(w, "Failed to get balance", http.StatusInternalServerError)
		return nil, false
	}
	// 检查余额是否足够
	if firstBalance <= 100 { // 0.1元 = 100积分
		http.Error(w, "Insufficient balance", http.StatusPaymentRequired)
		return nil, false
	}
	// 解析请求体
	var requestBody ChatRequest
	if err = json.NewDecoder(r.Body).Decode(&requestBody); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return nil, false
	}
	// 验证请求
	if requestBody.Content == "" || requestBody.PromptKey == "" {
		http.Error(w, "Invalid request: content, prompt_key are required and content_type must be 'image'", http.StatusBadRequest)
		return nil, false
	}
	return &AnalysisRequest{
		User:        user,
		RequestBody: requestBody,
		Context:     r.Context(),
	}, true
}

// performOCR 执行OCR处理
func performOCR(w http.ResponseWriter, req *AnalysisRequest) (*openai.ChatCompletionResponse, string, bool) {
	// 使用默认OCR模型
	ocrModel := "doubao-1-5-vision-pro-32k-250115"
	// 获取适合OCR模型的客户端
	ocrClient, err := GetClientForModel(ocrModel)
	if err != nil {
		http.Error(w, "Failed to create OCR API client", http.StatusInternalServerError)
		return nil, "", false
	}
	// OCR提示词
	ocrUserPrompt := `Role:教育专用手写OCR引擎
Action:准确识别图片中学生手写答题内容
Constraint:仅识别中文和英文,忽略划掉的文本,数学公式用LaTeX格式,如 (E=mc^2)
Input:用户上传的学生答题图片
Ouput: 只输出识别的内容，不要返回其他内容`
	// 发送带有图像的聊天完成请求进行OCR
	ocrResponse, err := ocrClient.CreateChatCompletionWithImage(
		req.Context,
		ocrModel,
		"",
		ocrUserPrompt,
		req.RequestBody.Content,
		0.3,
	)
	if err != nil {
		http.Error(w, fmt.Sprintf("OCR API error: %v", err), http.StatusInternalServerError)
		return nil, "", false
	}
	// 获取OCR结果
	ocrResult := ocrResponse.Choices[0].Message.Content
	return ocrResponse, ocrResult, true
}

// performAnalysis 执行分析处理
func performAnalysis(w http.ResponseWriter, req *AnalysisRequest, ocrResult string) (*openai.ChatCompletionResponse, string, bool) {
	// 使用用户指定的模型或默认模型
	analysisModel := req.RequestBody.Model
	if analysisModel == "" {
		analysisModel = "deepseek-v3-250324"
	}
	// 使用默认温度如果未指定
	temperature := req.RequestBody.Temperature
	if temperature == 0 {
		temperature = 0.3
	}
	// 获取系统提示词
	systemPrompt, _ := pg.GetSystemPromptForSubject(req.RequestBody.PromptKey)
	// 获取适合分析模型的客户端
	analysisClient, err := GetClientForModel(analysisModel)
	if err != nil {
		http.Error(w, "Failed to create analysis API client", http.StatusInternalServerError)
		return nil, "", false
	}
	// 构建分析提示词，结合OCR结果和用户提示词
	analysisPrompt := fmt.Sprintf("评分标准:\n%s\n\n学生答案:\n%s", req.RequestBody.Text, ocrResult)
	// 发送文本聊天完成请求进行分析
	analysisResponse, err := analysisClient.CreateChatCompletionWithText(
		req.Context,
		analysisModel,
		systemPrompt,
		analysisPrompt,
		temperature,
	)
	if err != nil {
		http.Error(w, fmt.Sprintf("Analysis API error: %v", err), http.StatusInternalServerError)
		return nil, "", false
	}
	return analysisResponse, analysisModel, true
}

// calculateCostAndConsume 计算成本并记录消费
func calculateCostAndConsume(req *AnalysisRequest, result *AnalysisResult) (int, error) {
	// 获取OCR和分析模型的价格
	ocrPricing := GetModelPricing(result.OcrModel)
	ocrCost := calculateAPICostInPoints(result.OcrResponse.Usage, ocrPricing)
	analysisPricing := GetModelPricing(result.AnalysisModel)
	analysisCost := calculateAPICostInPoints(result.AnalysisResponse.Usage, analysisPricing)
	// 总成本
	totalCost := ocrCost + analysisCost
	result.TotalCost = totalCost
	// 创建消费元数据
	metadata := map[string]any{
		"om":  modelId(result.OcrModel),                       // ocr_model
		"am":  modelId(result.AnalysisModel),                  // analysis_model
		"opt": result.OcrResponse.Usage.PromptTokens,          // ocr_prompt_tokens
		"oct": result.OcrResponse.Usage.CompletionTokens,      // ocr_completion_tokens
		"apt": result.AnalysisResponse.Usage.PromptTokens,     // analysis_prompt_tokens
		"act": result.AnalysisResponse.Usage.CompletionTokens, // analysis_completion_tokens
		"pk":  req.RequestBody.PromptKey,                      // prompt_key
		"dcm": DEFAULT_COST_MULTIPLIER,                        // cost_multiplier
	}
	// 记录消费
	consumeResult, err := pg.CheckAndConsume(
		req.User.ID.String(),
		totalCost,
		"",
		metadata,
	)
	if err != nil {
		// 如果记录消费失败，记录错误但仍然返回响应
		log.Printf("记录消费失败: %v\n", err)
	}
	// 获取更新后的余额
	var balance int
	if consumeResult != nil && consumeResult["success"].(bool) {
		balance = consumeResult["balance"].(int)
	} else {
		// 如果消费失败或未记录消费，尝试获取当前余额
		balance, _ = pg.GetUserBalance(req.User.ID.String())
	}
	return balance, nil
}

// buildAnalysisResponse 构建并发送响应
func buildAnalysisResponse(w http.ResponseWriter, r *http.Request, result *AnalysisResult, balance int) {
	// 提取需要的字段
	simplifiedResponse := SimplifiedResponse{
		ID:        result.AnalysisResponse.ID,
		OcrResult: result.OcrResult,
		Content:   result.AnalysisResponse.Choices[0].Message.Content,
		Balance:   balance,
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	// 检查是否有刷新令牌
	refreshToken := r.Header.Get("X-Refresh-Token")
	if refreshToken != "" {
		// 调用Supabase Auth API进行令牌刷新
		resp, err := authClient.client.RefreshToken(refreshToken)
		if err == nil && resp != nil {
			// 将新的访问令牌放入响应头
			w.Header().Set("X-New-Access-Token", resp.AccessToken)
		}
	}
	json.NewEncoder(w).Encode(simplifiedResponse)
}

// AnalysisHandler 处理分析请求
func AnalysisHandler(w http.ResponseWriter, r *http.Request) {
	// 验证请求
	req, ok := validateAnalysisRequest(w, r)
	if !ok {
		return
	}
	// 执行OCR处理
	ocrResponse, ocrResult, ok := performOCR(w, req)
	if !ok {
		return
	}
	// 执行分析处理
	analysisResponse, analysisModel, ok := performAnalysis(w, req, ocrResult)
	if !ok {
		return
	}
	// 构建结果
	result := &AnalysisResult{
		OcrResult:        ocrResult,
		AnalysisResponse: analysisResponse,
		OcrResponse:      ocrResponse,
		OcrModel:         "doubao-1-5-vision-pro-32k-250115",
		AnalysisModel:    analysisModel,
	}
	// 计算成本并记录消费
	balance, err := calculateCostAndConsume(req, result)
	if err != nil {
		log.Printf("计算成本失败: %v\n", err)
	}
	// 构建并发送响应
	buildAnalysisResponse(w, r, result, balance)
}
