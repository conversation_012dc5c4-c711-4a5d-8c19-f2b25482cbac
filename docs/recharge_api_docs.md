# 充值管理 API 文档

> **重要说明**：
>
> - 充值申请表和充值统计表中的 `amount` 和 `total_amount` 字段表示**人民币金额**
> - 钱包表和交易表中的 `balance` 和 `amount` 字段表示**积分**
> - 换算关系：**1 元人民币 = 1000 积分**

## 用户充值相关 API

### 1. 提交充值申请

**请求**:

- 方法: `POST`
- 路径: `/api/v1/recharge/request`
- 需要认证: 是

**请求体**:

```json
{
  "amount": 100, // 充值金额（人民币，必填）
  "order_id": "2023xxx", // 支付宝订单号（必填）
  "payment_proof": "https://example.com/proof.jpg" // 支付凭证URL（可选）
}
```

**响应**:

```json
{
  "success": true,
  "recharge_id": 123,
  "request_info": {
    "id": 123,
    "user_id": "user-uuid",
    "rmb_amount": 100, // 人民币金额
    "order_id": "2023xxx",
    "status": "pending",
    "payment_method": "alipay",
    "payment_proof": "https://example.com/proof.jpg",
    "created_at": 1633506000,
    "updated_at": 1633506000
  },
  "message": "充值申请已提交，等待管理员审核"
}
```

### 2. 查询用户充值申请列表

**请求**:

- 方法: `GET`
- 路径: `/api/v1/recharge/list?limit=20&offset=0`
- 需要认证: 是

**参数**:

- `limit`: 每页数量，默认 20
- `offset`: 偏移量，默认 0

**响应**:

```json
{
  "success": true,
  "requests": [
    {
      "id": 123,
      "user_id": "user-uuid",
      "rmb_amount": 100, // 人民币金额
      "order_id": "2023xxx",
      "status": "pending",
      "payment_method": "alipay",
      "payment_proof": "https://example.com/proof.jpg",
      "admin_note": "",
      "processed_by": "",
      "processed_at": 0,
      "created_at": 1633506000,
      "updated_at": 1633506000
    }
  ],
  "count": 1
}
```

## 管理员充值管理 API

### 1. 获取充值申请列表

**请求**:

- 方法: `GET`
- 路径: `/api/v1/admin/recharge/requests?status=pending&limit=50&offset=0`
- 需要认证: 是（管理员）

**参数**:

- `status`: 筛选状态（pending/approved/rejected），可选
- `limit`: 每页数量，默认 50
- `offset`: 偏移量，默认 0

**响应**:

```json
{
  "success": true,
  "requests": [
    {
      "id": 123,
      "user_id": "user-uuid",
      "rmb_amount": 100, // 人民币金额
      "order_id": "2023xxx",
      "status": "pending",
      "payment_method": "alipay",
      "payment_proof": "https://example.com/proof.jpg",
      "admin_note": "",
      "processed_by": "",
      "processed_at": 0,
      "created_at": 1633506000,
      "updated_at": 1633506000
    }
  ],
  "count": 1
}
```

### 2. 处理充值申请

**请求**:

- 方法: `POST`
- 路径: `/api/v1/admin/recharge/process`
- 需要认证: 是（管理员）

**请求体**:

```json
{
  "request_id": 123, // 充值申请ID（必填）
  "action": "approve", // 操作：approve或reject（必填）
  "note": "订单验证通过" // 处理备注（可选）
}
```

**响应**:

```json
{
  "success": true,
  "message": "充值申请处理成功"
}
```

### 3. 处理充值申请（通过订单号）

**请求**:

- 方法: `POST`
- 路径: `/api/v1/admin/recharge/process-by-order`
- 需要认证: 是（管理员）

**请求体**:

```json
{
  "order_id": "2023xxx", // 支付宝订单号（必填）
  "action": "approve", // 操作：approve或reject（必填）
  "note": "订单验证通过" // 处理备注（可选）
}
```

**响应**:

```json
{
  "success": true,
  "message": "充值申请处理成功"
}
```

### 4. 获取用户信息

**请求**:

- 方法: `GET`
- 路径: `/api/v1/admin/user/info?user_id=user-uuid`
- 需要认证: 是（管理员）

**参数**:

- `user_id`: 用户 ID（必填）

**响应**:

```json
{
  "user_id": "user-uuid",
  "balance": 200000, // 积分余额
  "total_recharge": 300 // 总充值金额（人民币）
}
```

### 5. 调整用户余额

**请求**:

- 方法: `POST`
- 路径: `/api/v1/admin/user/balance/adjust`
- 需要认证: 是（管理员）

**请求体**:

```json
{
  "user_id": "user-uuid", // 用户ID（必填）
  "amount": 50000, // 调整积分数量，正数为增加，负数为减少（必填）
  "reason": "活动奖励" // 调整原因（可选）
}
```

**响应**:

```json
{
  "success": true,
  "new_balance": 250000, // 调整后的积分余额
  "message": "用户余额调整成功"
}
```

### 6. 获取交易记录

**请求**:

- 方法: `POST`
- 路径: `/api/v1/admin/transactions`
- 需要认证: 是（管理员）

**请求体**:

```json
{
  "user_id": "user-uuid", // 用户ID（可选）
  "type": "deposit", // 交易类型：deposit或consumption（可选）
  "start_time": 1633420000, // 开始时间戳（可选）
  "end_time": 1633506000, // 结束时间戳（可选）
  "limit": 50, // 每页数量，默认50
  "offset": 0 // 偏移量，默认0
}
```

**响应**:

```json
{
  "success": true,
  "transactions": [
    {
      "id": 456,
      "user_id": "user-uuid",
      "type": "deposit",
      "amount": 100000, // 交易积分数量
      "balance_after": 200000, // 交易后的积分余额
      "description": "管理员批准充值 100 元人民币，兑换 100000 积分",
      "metadata": "{\"recharge_id\":123,\"admin_id\":\"admin-uuid\",\"note\":\"订单验证通过\"}",
      "created_at": 1633506000
    }
  ],
  "count": 1
}
```

### 7. 获取统计数据

**请求**:

- 方法: `POST`
- 路径: `/api/v1/admin/statistics`
- 需要认证: 是（管理员）

**请求体**:

```json
{
  "start_date": "2023-01-01", // 开始日期，格式YYYY-MM-DD（可选，默认30天前）
  "end_date": "2023-01-31" // 结束日期，格式YYYY-MM-DD（可选，默认今天）
}
```

**响应**:

```json
{
  "success": true,
  "statistics": [
    {
      "date": "2023-01-31",
      "total_amount": 500, // 当日总充值金额（人民币）
      "request_count": 6, // 当日充值请求数
      "approved_count": 5, // 当日批准充值数
      "rejected_count": 1, // 当日拒绝充值数
      "updated_at": 1633506000
    },
    {
      "date": "2023-01-30",
      "total_amount": 300, // 当日总充值金额（人民币）
      "request_count": 4, // 当日充值请求数
      "approved_count": 3, // 当日批准充值数
      "rejected_count": 1, // 当日拒绝充值数
      "updated_at": 1633419600
    }
  ],
  "count": 2
}
```

## 用户

### 1. 获取用户信息

**请求**:

- 方法: `GET`
- 路径: `/api/v1/admin/user/list`
- 需要认证: 是

**参数**:

- `limit`: 每页数量，默认 20
- `offset`: 偏移量，默认 0

**响应**:

```json
{
  "success": true,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "created_at": 200000, // 创建时间
    "last_sign_in_at": 300000 // 最后登录时间
  },
  "count": 1,
  "total": 10
}
```

## 状态码说明

- 200: 请求成功
- 400: 请求参数错误
- 401: 未授权，需要登录
- 403: 权限不足，不是管理员
- 500: 服务器内部错误

## 充值流程说明

1. 用户提交充值申请，包含充值金额（人民币）和支付宝订单号
2. 管理员在后台审核充值申请，验证支付宝订单
3. 管理员批准充值后，系统自动将人民币金额转换为积分（1 元人民币=1000 积分）并充值到用户账户
4. 用户可在前端查看充值记录和当前积分余额
