# 邮箱验证码登录 API 文档

本文档描述了通过邮箱验证码登录系统的 API 接口。

## 发送邮箱验证码

### 请求

- 方法: `POST`
- 路径: `/api/v1/auth/email-code`
- 身份验证: 不需要

#### 请求体

```json
{
  "email": "<EMAIL>"
}
```

| 字段  | 类型   | 描述         |
| ----- | ------ | ------------ |
| email | string | 用户邮箱地址 |

### 响应

#### 成功响应

- 状态码: `200 OK`
- 内容类型: `application/json`

```json
{
  "success": true,
  "message": "验证码已发送到您的邮箱，请查收"
}
```

#### 错误响应

- 状态码: `400 Bad Request`
- 内容类型: `text/plain`

```
发送验证码失败: {错误信息}
```

## 邮箱验证码登录

### 请求

- 方法: `POST`
- 路径: `/api/v1/auth/email-login`
- 身份验证: 不需要

#### 请求体

```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

| 字段  | 类型   | 描述         |
| ----- | ------ | ------------ |
| email | string | 用户邮箱地址 |
| code  | string | 收到的验证码 |

### 响应

#### 成功响应

- 状态码: `200 OK`
- 内容类型: `application/json`
- 响应头:
  - `X-Access-Token`: 访问令牌
  - `X-Refresh-Token`: 刷新令牌

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 3600,
  "expires_at": 1620000000,
  "config": [...],
  "subjects": [...]
}
```

| 字段          | 类型   | 描述                        |
| ------------- | ------ | --------------------------- |
| access_token  | string | 用于 API 访问的 JWT 令牌    |
| refresh_token | string | 用于刷新访问令牌的 JWT 令牌 |
| expires_in    | number | 访问令牌的有效期（秒）      |
| expires_at    | number | 访问令牌的过期时间戳        |
| config        | array  | 用户配置信息                |
| subjects      | array  | 可用科目信息                |

#### 错误响应

- 状态码: `400 Bad Request`
- 内容类型: `text/plain`

```
验证码验证失败: {错误信息}
```

## 状态码说明

| 状态码 | 描述                   |
| ------ | ---------------------- |
| 200    | 请求成功               |
| 400    | 请求参数错误或验证失败 |
| 405    | 请求方法不允许         |

## 邮箱验证码登录流程

1. 客户端调用发送邮箱验证码接口，向指定邮箱发送验证码
2. 用户从邮箱中获取验证码
3. 客户端调用邮箱验证码登录接口，提交邮箱和验证码
4. 服务器验证验证码，验证成功后返回访问令牌和刷新令牌
5. 客户端使用访问令牌访问需要身份验证的 API

## 重要说明

1. 验证码有效期有限，请在收到后尽快使用
2. 验证码登录成功后，使用方式与普通账号密码登录相同
3. 如果用户邮箱未注册，系统将无法发送验证码
