---
description: 
globs: 
alwaysApply: true
---
# 腾讯云函数项目结构指南

## 项目概述
这是一个基于 Go 语言开发的腾讯云函数项目，主要提供 AI 聊天和钱包管理功能。

## 核心文件结构
- [main.go](mdc:main.go) - 主入口文件，包含服务器配置和路由设置
- [auth.go](mdc:auth.go) - 认证相关功能实现
- [openapi.go](mdc:openapi.go) - OpenAPI 接口实现
- [wallet_handlers.go](mdc:wallet_handlers.go) - 钱包相关处理函数
- [turso_wallet.go](mdc:turso_wallet.go) - Turso 数据库钱包操作实现
- [config.go](mdc:config.go) - 配置文件管理

## API 端点
### 认证相关
- `/api/v1/auth/login` - 用户登录
- `/api/v1/auth/register` - 用户注册
- `/api/v1/auth/refresh` - 刷新令牌

### 聊天相关
- `/api/v2/chat/completions` - AI 聊天接口
- `/api/v2/chat/analysis` - OCR 分析接口

### 钱包相关
- `/api/v1/wallet/balance` - 查询余额
- `/api/v1/wallet/deposit` - 充值
- `/api/v1/wallet/transactions` - 交易记录

## 数据库
项目使用 Turso 作为数据库，相关配置在 [turso_config.go](mdc:turso_config.go) 中管理。

## 环境配置
- 使用 `.env` 文件管理环境变量
- 主要配置项包括：
  - Supabase URL 和 Key
  - Turso 数据库 URL 和认证令牌

## 开发注意事项
1. 所有受保护的 API 都需要通过认证中间件
2. 服务器默认监听 0.0.0.0:9000
3. 使用 `-e` 参数启动时会加载 .env 文件
4. 版本信息通过 `/api/v1/version` 接口提供

## 测试
- [auth_test.go](mdc:auth_test.go) - 认证功能测试
- [turso_wallet_test.go](mdc:turso_wallet_test.go) - 钱包功能测试
- [turso_prompts_test.go](mdc:turso_prompts_test.go) - 提示词功能测试
