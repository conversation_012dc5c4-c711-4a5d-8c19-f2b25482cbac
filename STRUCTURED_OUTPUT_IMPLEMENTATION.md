# Analysis API 结构化输出实现说明

## 概述

Analysis API 现在支持结构化输出，所有三种分析模式都会返回标准化的JSON格式结果，便于客户端解析和处理。

## 实现方案

### 1. 专业精批模式 (Professional Mode)

**特殊处理**: `deepseek-v3-250324` 模型不支持模型级结构化输出，因此使用提示词引导生成JSON格式。

#### 实现方式
- **OCR阶段**: 正常进行，提取学生答案文本
- **分析阶段**: 在提示词中明确要求JSON格式输出
- **解析**: 手动解析返回的JSON字符串

#### 输出结构
```json
{
  "score": <数字得分>,
  "grading_details": "<详细评分说明>"
}
```

#### 提示词示例
```
评分标准:
[用户提供的评分标准]

学生答案:
[OCR识别的答案]

请按照以下JSON格式输出分析结果：
{
  "score": <计算得到的最终数字得分>,
  "grading_details": "<根据评分标准，详细说明得分点的获取情况和扣分点的具体原因，需清晰对应学生作答内容>"
}
```

### 2. 智能均衡模式 (Standard Mode)

**模型**: `doubao-seed-1-6-250615` - 支持结构化输出

#### 实现方式
- **ResponseFormat**: 使用 `openai.ChatCompletionResponseFormatTypeJSONObject`
- **直接处理**: 一步完成图像理解和分析
- **自动解析**: 模型直接返回结构化JSON

#### 输出结构
```json
{
  "student_answer": "<从图片中提取的学生答案内容>",
  "score": <数字得分>,
  "grading_details": "<详细评分说明>"
}
```

### 3. 经济速览模式 (Economy Mode)

**模型**: `doubao-seed-1-6-flash-250615` - 支持结构化输出

#### 实现方式
- 与智能均衡模式相同
- 更快的处理速度，更低的成本
- 相同的输出结构

## 技术实现细节

### 新增数据结构

```go
// 专业模式分析结果结构
type ProfessionalAnalysisResult struct {
    Score          int    `json:"score"`
    GradingDetails string `json:"grading_details"`
}

// 标准/经济模式分析结果结构
type StandardAnalysisResult struct {
    StudentAnswer  string `json:"student_answer"`
    Score          int    `json:"score"`
    GradingDetails string `json:"grading_details"`
}
```

### 扩展的响应结构

```go
type SimplifiedResponse struct {
    ID           string       `json:"id"`
    OcrResult    string       `json:"ocr_result,omitempty"`    // 仅专业模式
    Content      string       `json:"content"`                 // 原始JSON字符串
    Analysis     interface{}  `json:"analysis,omitempty"`      // 解析后的结构化数据
    Balance      int          `json:"balance"`
    AnalysisMode AnalysisMode `json:"analysis_mode,omitempty"`
    Models       []string     `json:"models,omitempty"`
}
```

### 新增客户端方法

```go
// 支持结构化输出的图像分析
func (c *OpenAIClient) CreateChatCompletionWithImageAndResponseFormat(
    ctx context.Context,
    model string,
    systemPrompt string,
    userText string,
    imageURL string,
    temperature float32,
    responseFormat *openai.ChatCompletionResponseFormat,
) (*openai.ChatCompletionResponse, error)

// 支持结构化输出的文本分析
func (c *OpenAIClient) CreateChatCompletionWithTextAndResponseFormat(
    ctx context.Context,
    model string,
    systemPrompt string,
    userText string,
    temperature float32,
    responseFormat *openai.ChatCompletionResponseFormat,
) (*openai.ChatCompletionResponse, error)
```

### 解析逻辑

```go
func parseStructuredAnalysisResult(content string, mode AnalysisMode) (interface{}, error) {
    switch mode {
    case ProfessionalMode:
        var result ProfessionalAnalysisResult
        if err := json.Unmarshal([]byte(content), &result); err != nil {
            return nil, err
        }
        return result, nil
        
    case StandardMode, EconomyMode:
        var result StandardAnalysisResult
        if err := json.Unmarshal([]byte(content), &result); err != nil {
            return nil, err
        }
        return result, nil
    }
}
```

## API 响应示例

### 专业模式响应
```json
{
  "id": "chatcmpl-xxx",
  "ocr_result": "学生的手写答案内容...",
  "content": "{\"score\": 85, \"grading_details\": \"答案正确...\"}",
  "analysis": {
    "score": 85,
    "grading_details": "答案正确(50/50)，步骤完整(25/30)，书写清晰(10/20)。"
  },
  "balance": 9500,
  "analysis_mode": "professional",
  "models": ["doubao-1-5-vision-pro-32k-250115", "deepseek-v3-250324"]
}
```

### 标准/经济模式响应
```json
{
  "id": "chatcmpl-xxx",
  "content": "{\"student_answer\": \"x=2, y=3\", \"score\": 80, \"grading_details\": \"...\"}",
  "analysis": {
    "student_answer": "x = 2, y = 3, 解题过程：2x + y = 7",
    "score": 80,
    "grading_details": "答案正确，解题思路清晰，得分80分。"
  },
  "balance": 9700,
  "analysis_mode": "standard",
  "models": ["doubao-seed-1-6-250615"]
}
```

## 错误处理

1. **JSON解析失败**: 如果结构化解析失败，`analysis` 字段为 `null`，但仍返回原始 `content`
2. **模型不支持**: 专业模式使用提示词引导，其他模式使用ResponseFormat
3. **向后兼容**: 保留原始 `content` 字段，确保现有客户端正常工作

## 优势

1. **标准化**: 所有模式都返回一致的结构化数据
2. **易解析**: 客户端可以直接使用 `analysis` 字段的结构化数据
3. **向后兼容**: 保留原始 `content` 字段
4. **错误容错**: 解析失败时仍能获取原始内容
5. **类型安全**: 明确的数据结构定义

## 使用建议

1. **优先使用**: 客户端应优先使用 `analysis` 字段的结构化数据
2. **降级处理**: 如果 `analysis` 为空，可以解析 `content` 字段
3. **类型检查**: 根据 `analysis_mode` 判断 `analysis` 的具体类型
4. **错误处理**: 始终检查 `analysis` 是否为空

## 性能影响

- **专业模式**: 无额外性能开销（仅提示词变化）
- **标准/经济模式**: 轻微的JSON解析开销
- **总体**: 对API响应时间影响微乎其微
